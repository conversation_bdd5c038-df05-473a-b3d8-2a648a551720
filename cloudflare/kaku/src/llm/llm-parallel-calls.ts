/**
 * Helper functions for making parallel LLM calls with different schemas
 */

import { LLMRequest } from './types/llm-request';
import { ExtractionResult } from '../form-generation/types/form-interfaces';
import { ClassificationResult } from '../form-generation/types/classification-interfaces';
import {
  EXTRACTION_RESPONSE_SCHEMA,
  CLASSIFICATION_RESPONSE_SCHEMA,
} from './schemas/response-schemas';
import { LLMService } from './LLMService';
import { sanitizeJSONResponse } from '../workflow/utils/helpers';
import { PageStateResult } from '../agent/types/extract-result';

/**
 * Makes four parallel LLM calls with redundancy:
 * 1. Two extraction calls - extracts form controls and metadata
 * 2. Two classification calls - provides screen classification and verification codes
 * If one call fails during JSON parsing, other parallel calls can still succeed
 */
export async function makeParallelLLMCalls(
  llmService: LLMService,
  baseLLMRequest: Omit<LLMRequest, 'responseSchema' | 'prompt'>,
  extractionPrompt: string,
  classificationPrompt: string,
): Promise<PageStateResult> {
  // Create the extraction requests (2 identical requests for redundancy)
  const extractionRequest1: LLMRequest = {
    ...baseLLMRequest,
    prompt: extractionPrompt,
    responseSchema: EXTRACTION_RESPONSE_SCHEMA,
  };

  const extractionRequest2: LLMRequest = {
    ...baseLLMRequest,
    prompt: extractionPrompt,
    responseSchema: EXTRACTION_RESPONSE_SCHEMA,
  };

  // Create the classification requests (2 identical requests for redundancy)
  const classificationRequest1: LLMRequest = {
    ...baseLLMRequest,
    prompt: classificationPrompt,
    responseSchema: CLASSIFICATION_RESPONSE_SCHEMA,
  };

  const classificationRequest2: LLMRequest = {
    ...baseLLMRequest,
    prompt: classificationPrompt,
    responseSchema: CLASSIFICATION_RESPONSE_SCHEMA,
  };

  // Make all four calls in parallel
  const [
    extractionResponse1,
    extractionResponse2,
    classificationResponse1,
    classificationResponse2,
  ] = await Promise.all([
    llmService.getLLMResponse(extractionRequest1),
    llmService.getLLMResponse(extractionRequest2),
    llmService.getLLMResponse(classificationRequest1),
    llmService.getLLMResponse(classificationRequest2),
  ]);

  // Try to parse extraction results with fallback
  let extractionResult: ExtractionResult;
  try {
    extractionResult = sanitizeJSONResponse<ExtractionResult>(extractionResponse1.output_text);
  } catch (error) {
    console.warn('First extraction call failed to parse, trying second:', error);
    extractionResult = sanitizeJSONResponse<ExtractionResult>(extractionResponse2.output_text);
  }

  // Try to parse classification results with fallback
  let classificationResult: ClassificationResult;
  try {
    classificationResult = sanitizeJSONResponse<ClassificationResult>(
      classificationResponse1.output_text,
    );
  } catch (error) {
    console.warn('First classification call failed to parse, trying second:', error);
    classificationResult = sanitizeJSONResponse<ClassificationResult>(
      classificationResponse2.output_text,
    );
  }

  return {
    extractionResult,
    classificationResult,
  };
}
